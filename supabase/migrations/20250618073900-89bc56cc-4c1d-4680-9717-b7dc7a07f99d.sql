
-- Create storage bucket for organization logos
INSERT INTO storage.buckets (id, name, public)
VALUES ('org-logos', 'org-logos', true);

-- Create storage policies for the org-logos bucket
CREATE POLICY "Anyone can view org logos" ON storage.objects
FOR SELECT USING (bucket_id = 'org-logos');

CREATE POLICY "Anyone can upload org logos" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'org-logos');

CREATE POLICY "Anyone can update org logos" ON storage.objects
FOR UPDATE USING (bucket_id = 'org-logos');

CREATE POLICY "Anyone can delete org logos" ON storage.objects
FOR DELETE USING (bucket_id = 'org-logos');

-- Create table to store organization settings including logo
CREATE TABLE public.organization_settings (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  logo_url TEXT,
  logo_filename TEXT,
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on organization_settings
ALTER TABLE public.organization_settings ENABLE ROW LEVEL SECURITY;

-- Create policies for organization_settings
CREATE POLICY "Anyone can view organization settings" 
  ON public.organization_settings 
  FOR SELECT 
  TO public
  USING (true);

CREATE POLICY "Anyone can create organization settings" 
  ON public.organization_settings 
  FOR INSERT 
  TO public
  WITH CHECK (true);

CREATE POLICY "Anyone can update organization settings" 
  ON public.organization_settings 
  FOR UPDATE 
  TO public
  USING (true);

-- Insert initial record for organization settings
INSERT INTO public.organization_settings (logo_url, logo_filename)
VALUES (NULL, NULL);
