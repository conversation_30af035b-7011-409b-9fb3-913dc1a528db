
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Climate Disasters International Foundation | Empowering Climate Resilience</title>
    <meta name="description" content="CDIF strengthens disaster resilience in vulnerable communities through inclusive, community-driven disaster management programs in Cape Town, South Africa." />
    <meta name="author" content="Climate Disasters International Foundation" />
    <meta name="keywords" content="climate change, disaster management, community resilience, Cape Town, South Africa, nonprofit, disaster preparedness" />

    <!-- <PERSON>o Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" rel="stylesheet">

    <meta property="og:title" content="Climate Disasters International Foundation | Empowering Climate Resilience" />
    <meta property="og:description" content="Strengthening disaster resilience in vulnerable communities through evidence-based disaster management programs." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://climatedisastersinternationalfoundation.co.za" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@cdif_foundation" />
    <meta name="twitter:title" content="Climate Disasters International Foundation" />
    <meta name="twitter:description" content="Empowering communities to withstand climate disasters through inclusive disaster management programs." />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />
    
    <link rel="canonical" href="https://climatedisastersinternationalfoundation.co.za" />
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
