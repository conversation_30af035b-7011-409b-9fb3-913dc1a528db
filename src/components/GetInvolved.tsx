
import { Users, Heart, Calendar, Mail } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const GetInvolved = () => {
  const ways = [
    {
      icon: <Users className="h-8 w-8" />,
      title: "Become an Advocacy Ambassador",
      description: "Join our community of passionate advocates working to raise awareness and drive policy change for climate resilience.",
      action: "Sign Up as Volunteer",
      link: "/volunteer",
      color: "green"
    },
    {
      icon: <Heart className="h-8 w-8" />,
      title: "Make a Donation",
      description: "Support our life-saving programs and help us reach more vulnerable communities with essential disaster resilience resources.",
      action: "Donate Now",
      link: "/donate",
      color: "blue"
    },
    {
      icon: <Calendar className="h-8 w-8" />,
      title: "Attend Our Events",
      description: "Participate in fundraising events, workshops, and community gatherings to support our mission and connect with like-minded individuals.",
      action: "View Events",
      link: "/events",
      color: "purple"
    }
  ];

  return (
    <section id="get-involved" className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Get Involved
          </h2>
          <p className="text-lg text-gray-600 leading-relaxed">
            Join us in building a climate-resilient future. Together, we can help communities prepare for and recover from disasters while creating lasting change.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto mb-16">
          {ways.map((way, index) => (
            <div key={index} className="text-center p-8 bg-gray-50 rounded-lg hover:shadow-lg transition-shadow duration-300">
              <div className="inline-flex p-4 bg-green-100 rounded-full mb-6">
                <div className="text-green-600">
                  {way.icon}
                </div>
              </div>
              
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                {way.title}
              </h3>
              
              <p className="text-gray-600 mb-6 leading-relaxed text-base">
                {way.description}
              </p>
              
              <Link to={way.link}>
                <Button className="bg-green-600 hover:bg-green-700 text-white w-full">
                  {way.action}
                </Button>
              </Link>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="bg-green-600 text-white rounded-lg p-8 md:p-12 text-center">
          <h3 className="text-2xl md:text-3xl font-bold mb-4">
            Ready to Make a Difference?
          </h3>
          <p className="text-lg mb-8 opacity-90 max-w-2xl mx-auto">
            Join us in building a climate-resilient future—take action today to help communities prepare for and recover from disasters.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-green-600 hover:bg-gray-100">
              <Mail className="mr-2 h-5 w-5" />
              Contact Us
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600">
              Learn More About Our Impact
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GetInvolved;
