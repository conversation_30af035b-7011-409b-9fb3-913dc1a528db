
import React from 'react';
import { Input } from '@/components/ui/input';

interface LogoUploadInputProps {
  uploading: boolean;
  onUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const LogoUploadInput = ({ uploading, onUpload }: LogoUploadInputProps) => {
  return (
    <Input
      id="logo-upload"
      type="file"
      accept="image/*"
      onChange={onUpload}
      disabled={uploading}
      className="hidden"
    />
  );
};

export default LogoUploadInput;
