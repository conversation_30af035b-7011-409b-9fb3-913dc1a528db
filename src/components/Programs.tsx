
import { <PERSON>, AlertTriangle, Zap, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';

const Programs = () => {
  const programs = [
    {
      icon: <Shield className="h-8 w-8" />,
      title: "Disaster Mitigation",
      description: "Preventing climate-induced disasters and reducing their long-term impacts through proactive strategies.",
      initiatives: [
        "Community hazard mapping using GIS technology",
        "Climate-adaptive building construction",
        "Ecosystem protection and watershed management",
        "Educational awareness campaigns in schools and communities"
      ],
      color: "green"
    },
    {
      icon: <AlertTriangle className="h-8 w-8" />,
      title: "Disaster Preparedness",
      description: "Increasing community capacity to effectively anticipate, respond to, and recover from disasters.",
      initiatives: [
        "First aid and fire safety training",
        "Emergency drills and evacuation simulations",
        "Multi-platform early warning systems",
        "Emergency Resource Centers in high-risk zones"
      ],
      color: "blue"
    },
    {
      icon: <Zap className="h-8 w-8" />,
      title: "Disaster Response",
      description: "Providing timely, effective, and inclusive emergency support to impacted communities.",
      initiatives: [
        "24-hour Rapid Response Team deployment",
        "Emergency shelters with essential services",
        "Mobile medical teams and first aid",
        "Family reunification services"
      ],
      color: "orange"
    },
    {
      icon: <RefreshCw className="h-8 w-8" />,
      title: "Disaster Recovery",
      description: "Restoring livelihoods and infrastructure while enhancing resilience for future disasters.",
      initiatives: [
        "Post-disaster impact assessments",
        "Climate-resilient reconstruction projects",
        "Livelihood restoration and vocational training",
        "Long-term psychosocial support services"
      ],
      color: "purple"
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      green: {
        bg: "bg-green-50",
        icon: "text-green-600",
        border: "border-green-200",
        button: "bg-green-600 hover:bg-green-700"
      },
      blue: {
        bg: "bg-blue-50",
        icon: "text-blue-600",
        border: "border-blue-200",
        button: "bg-blue-600 hover:bg-blue-700"
      },
      orange: {
        bg: "bg-orange-50",
        icon: "text-orange-600",
        border: "border-orange-200",
        button: "bg-orange-600 hover:bg-orange-700"
      },
      purple: {
        bg: "bg-purple-50",
        icon: "text-purple-600",
        border: "border-purple-200",
        button: "bg-purple-600 hover:bg-purple-700"
      }
    };
    return colorMap[color as keyof typeof colorMap];
  };

  return (
    <section id="programs" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Our Programs
          </h2>
          <p className="text-lg text-gray-600 leading-relaxed">
            Our comprehensive approach covers the entire disaster management cycle, ensuring communities are prepared, protected, and resilient at every stage.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {programs.map((program, index) => {
            const colors = getColorClasses(program.color);
            return (
              <div key={index} className={`p-8 bg-white border-2 ${colors.border} rounded-lg hover:shadow-lg transition-all duration-300`}>
                <div className={`inline-flex p-3 ${colors.bg} rounded-lg mb-6`}>
                  <div className={colors.icon}>
                    {program.icon}
                  </div>
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {program.title}
                </h3>
                
                <p className="text-gray-600 mb-6 leading-relaxed text-base">
                  {program.description}
                </p>
                
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-3">Key Initiatives:</h4>
                  <ul className="space-y-2">
                    {program.initiatives.map((initiative, idx) => (
                      <li key={idx} className="flex items-start">
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <span className="text-gray-600 text-base">{initiative}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <Button className={`${colors.button} text-white`}>
                  Learn More
                </Button>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default Programs;
