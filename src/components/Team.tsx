import { useState, useEffect } from 'react';
import { Users, Edit, Linkedin, Mail, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface TeamMember {
  id: string;
  name: string;
  position: string;
  portfolio: string;
  image: string;
  email?: string;
  linkedin?: string;
  display_order?: number;
}

const Team = () => {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [editingMember, setEditingMember] = useState<TeamMember | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  // Load team members from Supabase
  useEffect(() => {
    fetchTeamMembers();
  }, []);

  const fetchTeamMembers = async () => {
    try {
      const { data, error } = await supabase
        .from('team_members')
        .select('*')
        .order('display_order', { ascending: true });

      if (error) {
        console.error('Error fetching team members:', error);
        toast({
          title: "Error",
          description: "Failed to load team members",
          variant: "destructive"
        });
        return;
      }

      setTeamMembers(data || []);
    } catch (error) {
      console.error('Error fetching team members:', error);
      toast({
        title: "Error",
        description: "Failed to load team members",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditMember = (member: TeamMember) => {
    setEditingMember(member);
    setIsAddingNew(false);
    setIsDialogOpen(true);
  };

  const handleAddNewMember = () => {
    const newMember: TeamMember = {
      id: '',
      name: '',
      position: '',
      portfolio: '',
      image: '/placeholder.svg',
      email: '',
      linkedin: '',
      display_order: teamMembers.length + 1
    };
    setEditingMember(newMember);
    setIsAddingNew(true);
    setIsDialogOpen(true);
  };

  const handleSaveMember = async (updatedMember: TeamMember) => {
    try {
      if (isAddingNew) {
        const { data, error } = await supabase
          .from('team_members')
          .insert([{
            name: updatedMember.name,
            position: updatedMember.position,
            portfolio: updatedMember.portfolio,
            image: updatedMember.image,
            email: updatedMember.email || null,
            linkedin: updatedMember.linkedin || null,
            display_order: updatedMember.display_order
          }])
          .select()
          .single();

        if (error) {
          console.error('Error adding team member:', error);
          toast({
            title: "Error",
            description: "Failed to add team member",
            variant: "destructive"
          });
          return;
        }

        setTeamMembers(prev => [...prev, data]);
        toast({
          title: "Success",
          description: "Team member added successfully"
        });
      } else {
        const { error } = await supabase
          .from('team_members')
          .update({
            name: updatedMember.name,
            position: updatedMember.position,
            portfolio: updatedMember.portfolio,
            image: updatedMember.image,
            email: updatedMember.email || null,
            linkedin: updatedMember.linkedin || null,
            updated_at: new Date().toISOString()
          })
          .eq('id', updatedMember.id);

        if (error) {
          console.error('Error updating team member:', error);
          toast({
            title: "Error",
            description: "Failed to update team member",
            variant: "destructive"
          });
          return;
        }

        setTeamMembers(prev => 
          prev.map(member => 
            member.id === updatedMember.id ? updatedMember : member
          )
        );
        toast({
          title: "Success",
          description: "Team member updated successfully"
        });
      }
    } catch (error) {
      console.error('Error saving team member:', error);
      toast({
        title: "Error",
        description: "Failed to save team member",
        variant: "destructive"
      });
    }

    setIsDialogOpen(false);
    setEditingMember(null);
    setIsAddingNew(false);
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>, setFormData: any) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const imageUrl = event.target?.result as string;
        setFormData((prev: any) => ({ ...prev, image: imageUrl }));
      };
      reader.readAsDataURL(file);
    }
  };

  const TeamMemberDialog = () => {
    const [formData, setFormData] = useState(editingMember || {
      id: '',
      name: '',
      position: '',
      portfolio: '',
      image: '/placeholder.svg',
      email: '',
      linkedin: '',
      display_order: teamMembers.length + 1
    });

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      if (formData.name && formData.position && formData.portfolio) {
        handleSaveMember(formData as TeamMember);
      }
    };

    return (
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{isAddingNew ? 'Add New Team Member' : 'Edit Team Member'}</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="image">Profile Picture</Label>
              <div className="flex items-center space-x-4 mt-2">
                <img
                  src={formData.image}
                  alt="Preview"
                  className="w-16 h-16 rounded-full object-cover border"
                />
                <div>
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleImageUpload(e, setFormData)}
                    className="mb-2"
                  />
                  <p className="text-xs text-gray-500">Upload a new profile picture</p>
                </div>
              </div>
            </div>
            <div>
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                required
              />
            </div>
            <div>
              <Label htmlFor="position">Position</Label>
              <Input
                id="position"
                value={formData.position}
                onChange={(e) => setFormData(prev => ({ ...prev, position: e.target.value }))}
                required
              />
            </div>
            <div>
              <Label htmlFor="portfolio">Portfolio Description</Label>
              <Textarea
                id="portfolio"
                value={formData.portfolio}
                onChange={(e) => setFormData(prev => ({ ...prev, portfolio: e.target.value }))}
                rows={3}
                required
              />
            </div>
            <div>
              <Label htmlFor="email">Email (Optional)</Label>
              <Input
                id="email"
                type="email"
                value={formData.email || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="linkedin">LinkedIn (Optional)</Label>
              <Input
                id="linkedin"
                value={formData.linkedin || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, linkedin: e.target.value }))}
                placeholder="https://linkedin.com/in/username"
              />
            </div>
            <Button type="submit" className="w-full">
              {isAddingNew ? 'Add Team Member' : 'Save Changes'}
            </Button>
          </form>
        </DialogContent>
      </Dialog>
    );
  };

  if (loading) {
    return (
      <section id="team" className="py-20 bg-gradient-to-br from-emerald-50/50 via-white to-blue-50/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-xl text-gray-600">Loading team members...</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="team" className="py-20 bg-gradient-to-br from-emerald-50/50 via-white to-blue-50/50 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-20 left-10 w-72 h-72 bg-emerald-200 rounded-full mix-blend-multiply filter blur-xl animate-float"></div>
        <div className="absolute top-40 right-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-20 left-1/2 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl animate-float" style={{ animationDelay: '4s' }}></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header Section */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center p-3 bg-emerald-100 rounded-full mb-6">
            <Users className="h-8 w-8 text-emerald-600" />
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Meet Our <span className="text-emerald-600">Team</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8 leading-relaxed">
            Passionate professionals working together to strengthen climate resilience in communities across South Africa and beyond.
          </p>
        </div>

        {/* Team Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {teamMembers.map((member, index) => (
            <div
              key={member.id}
              className="group animate-fade-in-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <Card className="relative overflow-hidden bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 hover:scale-105">
                {/* Image Section */}
                <div className="relative overflow-hidden">
                  <div className="aspect-square w-full">
                    <img
                      src={member.image}
                      alt={member.name}
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                    />
                  </div>
                  
                  {/* Gradient Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  
                  {/* Edit Button */}
                  <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => handleEditMember(member)}
                      className="bg-white/90 hover:bg-white shadow-lg backdrop-blur-sm rounded-full p-2"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Social Links Overlay */}
                  {(member.email || member.linkedin) && (
                    <div className="absolute bottom-3 left-3 flex space-x-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                      {member.email && (
                        <a
                          href={`mailto:${member.email}`}
                          className="p-2 bg-white/90 hover:bg-white rounded-full text-emerald-600 hover:text-emerald-700 transition-colors shadow-lg backdrop-blur-sm"
                        >
                          <Mail className="h-4 w-4" />
                        </a>
                      )}
                      {member.linkedin && (
                        <a
                          href={member.linkedin}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 bg-white/90 hover:bg-white rounded-full text-emerald-600 hover:text-emerald-700 transition-colors shadow-lg backdrop-blur-sm"
                        >
                          <Linkedin className="h-4 w-4" />
                        </a>
                      )}
                    </div>
                  )}
                </div>

                {/* Content Section */}
                <CardContent className="p-6">
                  <div className="text-center">
                    <h3 className="text-lg font-bold text-gray-900 mb-1 group-hover:text-emerald-600 transition-colors duration-300">
                      {member.name}
                    </h3>
                    <p className="text-emerald-600 font-medium text-sm mb-3">
                      {member.position}
                    </p>
                    <p className="text-gray-600 text-sm leading-relaxed line-clamp-3">
                      {member.portfolio}
                    </p>
                  </div>
                </CardContent>

                {/* Decorative Border */}
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-full transition-all duration-300 group-hover:w-full"></div>
              </Card>
            </div>
          ))}
        </div>

        <TeamMemberDialog />
      </div>
    </section>
  );
};

export default Team;
