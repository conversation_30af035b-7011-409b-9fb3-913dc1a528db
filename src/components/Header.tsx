
import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, SheetDescription, <PERSON>etHeader, She<PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from "@/components/ui/sheet"
import { Menu, Settings } from "lucide-react";
import { ModeToggle } from "@/components/ModeToggle"
import { Button } from "@/components/ui/button";
import { supabase } from '@/integrations/supabase/client';
import { Link } from 'react-router-dom';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [orgLogo, setOrgLogo] = useState<string | null>(null);

  useEffect(() => {
    fetchOrgLogo();
  }, []);

  const fetchOrgLogo = async () => {
    try {
      const { data, error } = await supabase
        .from('organization_settings')
        .select('logo_url')
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching logo:', error);
        return;
      }

      if (data?.logo_url) {
        setOrgLogo(data.logo_url);
      }
    } catch (error) {
      console.error('Error fetching organization logo:', error);
    }
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    closeMenu();
  };

  const navItems = [
    { name: 'Home', href: '#home' },
    { name: 'About', href: '#about' },
    { name: 'Programs', href: '#programs' },
    { name: 'Team', href: '#team' },
    { name: 'News', href: '#news' },
    { name: 'Get Involved', href: '#get-involved' },
    { name: 'Contact', href: '#contact' },
  ];

  return (
    <header className="bg-background/95 backdrop-blur-md sticky top-0 z-50 shadow-sm border-b border-border/20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-24">
          {/* Logo Section */}
          <div className="flex-shrink-0">
            <a href="/" className="flex items-center gap-4 group">
              {orgLogo ? (
                <img 
                  src={orgLogo} 
                  alt="Organization Logo" 
                  className="h-16 w-16 object-contain transition-transform duration-300 group-hover:scale-105" 
                />
              ) : (
                <img 
                  src="/logo.png" 
                  alt="Climate Disasters International Foundation Logo" 
                  className="h-16 w-16 object-contain transition-transform duration-300 group-hover:scale-105" 
                />
              )}
              <div className="hidden sm:flex flex-col">
                <span className="font-roboto font-bold text-2xl text-foreground leading-tight tracking-tight">
                  Climate Disasters
                </span>
                <span className="font-roboto font-semibold text-lg text-muted-foreground leading-tight mt-1">
                  International Foundation
                </span>
              </div>
            </a>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center">
            <div className="flex items-center space-x-2">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  onClick={(e) => {
                    e.preventDefault();
                    scrollToSection(item.href.substring(1));
                  }}
                  className="relative px-4 py-3 text-lg font-medium text-foreground/70 hover:text-foreground transition-all duration-200 rounded-md hover:bg-accent/50 group"
                >
                  {item.name}
                  <span className="absolute bottom-1 left-1/2 -translate-x-1/2 w-0 h-0.5 bg-primary transition-all duration-200 group-hover:w-4/5 rounded-full"></span>
                </a>
              ))}
            </div>
            
            {/* Action Buttons */}
            <div className="flex items-center space-x-2 ml-8 pl-8 border-l border-border/30">
              <Link to="/settings">
                <Button variant="ghost" size="icon" className="h-10 w-10 hover:bg-accent transition-colors">
                  <Settings className="h-5 w-5" />
                </Button>
              </Link>
              <ModeToggle />
            </div>
          </nav>

          {/* Mobile Navigation */}
          <div className="flex items-center space-x-2 lg:hidden">
            <Link to="/settings">
              <Button variant="ghost" size="icon" className="h-10 w-10">
                <Settings className="h-5 w-5" />
              </Button>
            </Link>
            <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <SheetTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={toggleMenu} 
                  className="h-10 w-10"
                >
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80">
                <SheetHeader className="text-left">
                  <SheetTitle className="text-xl font-semibold">Navigation</SheetTitle>
                  <SheetDescription className="text-base text-muted-foreground">
                    Explore our organization and initiatives.
                  </SheetDescription>
                </SheetHeader>
                <nav className="grid gap-1 mt-8">
                  {navItems.map((item) => (
                    <a
                      key={item.name}
                      href={item.href}
                      onClick={(e) => {
                        e.preventDefault();
                        scrollToSection(item.href.substring(1));
                      }}
                      className="flex items-center font-medium text-lg py-4 px-4 rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-200"
                    >
                      {item.name}
                    </a>
                  ))}
                  <div className="pt-4 mt-4 border-t border-border/50">
                    <div className="px-4">
                      <ModeToggle />
                    </div>
                  </div>
                </nav>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
