
import { Calendar, Heart, Users, Target } from 'lucide-react';

const About = () => {
  const values = [
    {
      icon: <Users className="h-6 w-6" />,
      title: "Inclusivity",
      description: "Ensuring all community members, especially the most vulnerable, are included in our programs."
    },
    {
      icon: <Target className="h-6 w-6" />,
      title: "Community Empowerment",
      description: "Building local capacity and empowering communities to lead their own resilience efforts."
    },
    {
      icon: <Heart className="h-6 w-6" />,
      title: "Collaboration",
      description: "Working together with communities, partners, and stakeholders for maximum impact."
    },
    {
      icon: <Calendar className="h-6 w-6" />,
      title: "Evidence-Based Approach",
      description: "Using research and data to inform our strategies and measure our impact."
    }
  ];

  return (
    <section id="about" className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Mission & Vision */}
        <div className="max-w-4xl mx-auto text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Our Mission & Vision
          </h2>
          
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <div className="p-6 bg-green-50 rounded-lg">
              <h3 className="text-xl font-semibold text-green-800 mb-4">Mission</h3>
              <p className="text-gray-700 leading-relaxed text-base">
                To strengthen disaster resilience in vulnerable communities by equipping them with the knowledge, skills, and resources needed to prevent, prepare for, respond to, and recover from climatic disasters.
              </p>
            </div>
            
            <div className="p-6 bg-blue-50 rounded-lg">
              <h3 className="text-xl font-semibold text-blue-800 mb-4">Vision</h3>
              <p className="text-gray-700 leading-relaxed text-base">
                A world where every community is climate-resilient, empowered, and equipped with sustainable disaster management strategies, ensuring sustainable development and climate adaptation for future generations.
              </p>
            </div>
          </div>
        </div>

        {/* Our Story */}
        <div className="max-w-4xl mx-auto mb-16">
          <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6 text-center">Our Story</h3>
          
          <div className="bg-gray-50 p-8 rounded-lg">
            <div className="space-y-6 text-gray-700 leading-relaxed text-base">
              <p>
                Originally founded in 2021 as a mental health organization, our journey was sparked by a deep passion for community well-being. Our initiatives included providing mental health education in various community-based organizations, schools, and local groups.
              </p>
              
              <p>
                Over time, we witnessed the growing intersection between mental health and the impacts of climate-induced disasters. In late 2023, South Africa experienced a series of devastating natural disasters that revealed the urgent need for enhanced disaster preparedness and community resilience.
              </p>
              
              <p>
                The severe flooding in the Western Cape on September 24–25, 2023, which severely affected the Siyahlala Informal Settlement in Dunoon and the Kosovo Informal Settlement, followed by flash floods in Ladysmith on Christmas Day that destroyed approximately 1,400 homes and claimed 21 lives, exposed critical gaps in disaster management.
              </p>
              
              <p>
                In response, Climate Disasters International Foundation was officially established in November 2024 by Ropafadzo Bopoto and Kathy-leigh Oliphant with a clear mission to strengthen disaster resilience in vulnerable communities.
              </p>
            </div>
          </div>
        </div>

        {/* Core Values */}
        <div className="max-w-6xl mx-auto">
          <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-8 text-center">Core Values</h3>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <div key={index} className="p-6 bg-white border border-gray-200 rounded-lg hover:shadow-lg transition-shadow duration-200">
                <div className="text-green-600 mb-4">
                  {value.icon}
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-3">{value.title}</h4>
                <p className="text-gray-600 text-base">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
