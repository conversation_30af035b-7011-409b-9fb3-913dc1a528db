
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Upload, Image as ImageIcon, Trash2 } from 'lucide-react';

interface LogoDisplayProps {
  currentLogo: string | null;
  uploading: boolean;
  onDelete: () => void;
}

const LogoDisplay = ({ currentLogo, uploading, onDelete }: LogoDisplayProps) => {
  if (currentLogo) {
    return (
      <div className="flex flex-col items-center space-y-4">
        <div className="relative">
          <img 
            src={currentLogo} 
            alt="Organization Logo" 
            className="h-32 w-32 object-contain border-2 border-gray-200 rounded-lg"
          />
        </div>
        <div className="flex space-x-2">
          <label htmlFor="logo-upload">
            <Button asChild variant="outline" disabled={uploading}>
              <span className="cursor-pointer">
                <Upload className="h-4 w-4 mr-2" />
                {uploading ? 'Uploading...' : 'Replace Logo'}
              </span>
            </Button>
          </label>
          <Button 
            variant="destructive" 
            onClick={onDelete}
            disabled={uploading}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center space-y-4">
      <div className="h-32 w-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
        <ImageIcon className="h-8 w-8 text-gray-400" />
      </div>
      <label htmlFor="logo-upload">
        <Button asChild disabled={uploading}>
          <span className="cursor-pointer">
            <Upload className="h-4 w-4 mr-2" />
            {uploading ? 'Uploading...' : 'Upload Logo'}
          </span>
        </Button>
      </label>
    </div>
  );
};

export default LogoDisplay;
