
import { Calendar, ExternalLink, Globe, MapPin, RefreshCw } from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { useClimateNews } from '@/hooks/useClimateNews';
import NewsLoadingSkeleton from './NewsLoadingSkeleton';

const News = () => {
  const { data: newsItems, isLoading, error, refetch, isFetching } = useClimateNews();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const handleRefresh = () => {
    refetch();
  };

  if (error) {
    console.error('Error loading news:', error);
  }

  return (
    <section id="news" className="py-20 bg-gradient-to-br from-gray-50 via-white to-emerald-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-emerald-100 text-emerald-700 mb-6">
            📰 Latest Climate News
          </div>
          <div className="flex items-center justify-center gap-4 mb-6">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 font-inter">
              Climate News &{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 to-green-600">
                Updates
              </span>
            </h2>
            <Button
              onClick={handleRefresh}
              variant="ghost"
              size="sm"
              className="text-emerald-600 hover:text-emerald-700 hover:bg-emerald-50"
              disabled={isFetching}
            >
              <RefreshCw className={`w-5 h-5 ${isFetching ? 'animate-spin' : ''}`} />
            </Button>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-inter font-light">
            Stay informed about climate change impacts and resilience efforts in South Africa and around the world
          </p>
          {isFetching && !isLoading && (
            <div className="inline-flex items-center px-3 py-1 rounded-full text-sm text-emerald-600 bg-emerald-50 mt-4">
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              Updating news...
            </div>
          )}
        </div>

        {/* News Content */}
        {isLoading ? (
          <NewsLoadingSkeleton />
        ) : (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {newsItems?.slice(0, 6).map((item, index) => (
              <Card 
                key={item.id} 
                className="group hover:shadow-2xl transition-all duration-500 border-0 bg-white/80 backdrop-blur-sm overflow-hidden animate-fade-in-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* Image */}
                <div className="relative overflow-hidden">
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute top-4 left-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${
                      item.isLocal 
                        ? 'bg-emerald-100 text-emerald-700' 
                        : 'bg-blue-100 text-blue-700'
                    }`}>
                      {item.isLocal ? <MapPin className="w-3 h-3 mr-1" /> : <Globe className="w-3 h-3 mr-1" />}
                      {item.category}
                    </span>
                  </div>
                </div>

                <CardHeader className="pb-3">
                  <div className="flex items-center text-sm text-gray-500 mb-2">
                    <Calendar className="w-4 h-4 mr-2" />
                    {formatDate(item.date)}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 leading-tight font-inter group-hover:text-emerald-600 transition-colors">
                    {item.title}
                  </h3>
                </CardHeader>

                <CardContent className="pt-0">
                  <p className="text-gray-600 text-base mb-4 leading-relaxed font-inter">
                    {item.excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-xs text-gray-500">
                      <MapPin className="w-3 h-3 mr-1" />
                      {item.location}
                    </div>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      className="text-emerald-600 hover:text-emerald-700 hover:bg-emerald-50 p-2"
                      onClick={() => item.url && window.open(item.url, '_blank')}
                    >
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* View More Button */}
        <div className="text-center">
          <Link to="/news">
            <Button 
              size="lg" 
              className="bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white px-8 py-4 rounded-xl text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
            >
              View All Climate News
              <ExternalLink className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default News;
