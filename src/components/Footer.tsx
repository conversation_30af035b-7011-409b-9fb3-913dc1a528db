
import { Mail, Phone, MapPin, Facebook, Twitter, Linkedin, Instagram } from 'lucide-react';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Organization Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">C</span>
              </div>
              <span className="font-bold text-xl">CDIF</span>
            </div>
            <h3 className="text-lg font-semibold mb-3">Climate Disasters International Foundation</h3>
            <p className="text-gray-300 mb-4 leading-relaxed">
              Strengthening disaster resilience in vulnerable communities through inclusive, community-driven, and evidence-based disaster management programs.
            </p>
            <div className="space-y-2 text-sm text-gray-300">
              <div className="flex items-center">
                <Mail className="h-4 w-4 mr-2" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center">
                <Phone className="h-4 w-4 mr-2" />
                <span>+27 72 745 0595</span>
              </div>
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-2" />
                <span>Cape Town, South Africa</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2 text-gray-300">
              <li>
                <a href="#about" className="hover:text-green-400 transition-colors">About Us</a>
              </li>
              <li>
                <a href="#programs" className="hover:text-green-400 transition-colors">Our Programs</a>
              </li>
              <li>
                <a href="#get-involved" className="hover:text-green-400 transition-colors">Get Involved</a>
              </li>
              <li>
                <a href="#contact" className="hover:text-green-400 transition-colors">Contact</a>
              </li>
            </ul>
          </div>

          {/* Programs */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Our Programs</h4>
            <ul className="space-y-2 text-gray-300">
              <li>
                <a href="#programs" className="hover:text-green-400 transition-colors">Disaster Mitigation</a>
              </li>
              <li>
                <a href="#programs" className="hover:text-green-400 transition-colors">Disaster Preparedness</a>
              </li>
              <li>
                <a href="#programs" className="hover:text-green-400 transition-colors">Disaster Response</a>
              </li>
              <li>
                <a href="#programs" className="hover:text-green-400 transition-colors">Disaster Recovery</a>
              </li>
            </ul>
          </div>
        </div>

        {/* Social Media & Bottom */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex space-x-4 mb-4 md:mb-0">
              <a href="#" className="text-gray-400 hover:text-green-400 transition-colors">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-green-400 transition-colors">
                <Twitter className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-green-400 transition-colors">
                <Linkedin className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-green-400 transition-colors">
                <Instagram className="h-5 w-5" />
              </a>
            </div>
            
            <div className="text-center md:text-right text-gray-400 text-sm">
              <p>&copy; {currentYear} Climate Disasters International Foundation. All rights reserved.</p>
              <p className="mt-1">climatedisastersinternationalfoundation.co.za</p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
