
import { TrendingUp, Thermometer, CloudRain, Shield, AlertTriangle, Users } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

const ClimateInfo = () => {
  const statistics = [
    {
      icon: <Thermometer className="h-8 w-8" />,
      title: "Global Temperature Rise",
      value: "1.1°C",
      description: "Since pre-industrial times",
      trend: "+0.18°C per decade",
      color: "text-red-600"
    },
    {
      icon: <CloudRain className="h-8 w-8" />,
      title: "Sea Level Rise",
      value: "21-24cm",
      description: "Since 1880",
      trend: "3.3mm per year",
      color: "text-blue-600"
    },
    {
      icon: <AlertTriangle className="h-8 w-8" />,
      title: "Climate Disasters",
      value: "432",
      description: "Natural disasters in 2023",
      trend: "22% increase from 2022",
      color: "text-orange-600"
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: "People Affected",
      value: "95.6M",
      description: "By climate disasters in 2023",
      trend: "Increasing annually",
      color: "text-purple-600"
    }
  ];

  const impacts = [
    {
      region: "South Africa",
      impact: "Severe droughts and flooding",
      vulnerability: 85,
      description: "Increased frequency of extreme weather events affecting agriculture and water resources"
    },
    {
      region: "Sub-Saharan Africa",
      impact: "Food insecurity",
      vulnerability: 78,
      description: "Climate change threatening food production and increasing malnutrition rates"
    },
    {
      region: "Coastal Areas",
      impact: "Sea level rise",
      vulnerability: 72,
      description: "Rising seas threatening coastal communities and infrastructure"
    },
    {
      region: "Informal Settlements",
      impact: "Extreme vulnerability",
      vulnerability: 92,
      description: "Limited infrastructure and resources to cope with climate disasters"
    }
  ];

  const solutions = [
    {
      title: "Early Warning Systems",
      description: "Implementing community-based early warning systems to reduce disaster impact",
      progress: 65
    },
    {
      title: "Climate-Resilient Infrastructure",
      description: "Building infrastructure that can withstand extreme weather events",
      progress: 42
    },
    {
      title: "Community Preparedness",
      description: "Training communities in disaster preparedness and response",
      progress: 78
    },
    {
      title: "Sustainable Agriculture",
      description: "Promoting climate-smart farming practices for food security",
      progress: 55
    }
  ];

  return (
    <section id="climate-info" className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Climate Change <span className="text-emerald-600">Facts</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Understanding the current state of climate change and its impacts on vulnerable communities
          </p>
        </div>

        {/* Key Statistics */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {statistics.map((stat, index) => (
            <Card key={index} className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className={`mx-auto mb-4 ${stat.color}`}>
                  {stat.icon}
                </div>
                <CardTitle className="text-lg">{stat.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className={`text-3xl font-bold mb-2 ${stat.color}`}>
                  {stat.value}
                </div>
                <p className="text-gray-600 text-sm mb-2">{stat.description}</p>
                <p className="text-xs text-gray-500">{stat.trend}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Regional Impacts */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Regional Climate Impacts</h3>
          <div className="grid md:grid-cols-2 gap-6">
            {impacts.map((impact, index) => (
              <Card key={index} className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900">{impact.region}</h4>
                    <p className="text-emerald-600 font-medium">{impact.impact}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900">{impact.vulnerability}%</div>
                    <div className="text-xs text-gray-500">Vulnerability</div>
                  </div>
                </div>
                <Progress value={impact.vulnerability} className="mb-3" />
                <p className="text-gray-600 text-sm">{impact.description}</p>
              </Card>
            ))}
          </div>
        </div>

        {/* Solutions Progress */}
        <div>
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Our Solutions in Action</h3>
          <div className="grid md:grid-cols-2 gap-6">
            {solutions.map((solution, index) => (
              <Card key={index} className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">{solution.title}</h4>
                    <p className="text-gray-600 text-sm">{solution.description}</p>
                  </div>
                  <div className="ml-4 text-right">
                    <div className="text-xl font-bold text-emerald-600">{solution.progress}%</div>
                    <div className="text-xs text-gray-500">Complete</div>
                  </div>
                </div>
                <Progress value={solution.progress} className="mt-4" />
              </Card>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-gradient-to-r from-emerald-600 to-green-600 text-white rounded-lg p-8 mt-16 text-center">
          <Shield className="h-12 w-12 mx-auto mb-4 opacity-90" />
          <h3 className="text-2xl font-bold mb-4">Together We Can Build Resilience</h3>
          <p className="text-lg opacity-90 mb-6 max-w-2xl mx-auto">
            Join us in creating sustainable solutions that protect vulnerable communities from climate disasters.
          </p>
        </div>
      </div>
    </section>
  );
};

export default ClimateInfo;
