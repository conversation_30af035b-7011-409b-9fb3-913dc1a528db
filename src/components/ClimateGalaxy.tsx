
import React from 'react';
import { Leaf, Droplets, Wind, Sun, Cloud, TreePine, Recycle, Globe } from 'lucide-react';

const ClimateGalaxy = () => {
  // Climate objects with different icons, sizes, and animation delays
  const climateObjects = [
    { Icon: Leaf, size: 'w-4 h-4', color: 'text-emerald-400', delay: '0s', x: '10%', y: '20%' },
    { Icon: Droplets, size: 'w-3 h-3', color: 'text-blue-400', delay: '1s', x: '80%', y: '15%' },
    { Icon: Wind, size: 'w-5 h-5', color: 'text-green-300', delay: '2s', x: '60%', y: '30%' },
    { Icon: Sun, size: 'w-6 h-6', color: 'text-yellow-400', delay: '0.5s', x: '20%', y: '60%' },
    { Icon: Cloud, size: 'w-4 h-4', color: 'text-gray-300', delay: '1.5s', x: '75%', y: '70%' },
    { Icon: TreePine, size: 'w-5 h-5', color: 'text-emerald-500', delay: '3s', x: '40%', y: '80%' },
    { Icon: Recycle, size: 'w-4 h-4', color: 'text-green-400', delay: '2.5s', x: '90%', y: '50%' },
    { Icon: Globe, size: 'w-6 h-6', color: 'text-emerald-600', delay: '4s', x: '30%', y: '10%' },
    { Icon: Leaf, size: 'w-3 h-3', color: 'text-green-300', delay: '1.8s', x: '15%', y: '45%' },
    { Icon: Droplets, size: 'w-5 h-5', color: 'text-cyan-400', delay: '3.2s', x: '70%', y: '25%' },
    { Icon: Wind, size: 'w-3 h-3', color: 'text-emerald-300', delay: '0.8s', x: '50%', y: '65%' },
    { Icon: TreePine, size: 'w-4 h-4', color: 'text-green-500', delay: '2.8s', x: '85%', y: '35%' },
    { Icon: Recycle, size: 'w-3 h-3', color: 'text-emerald-400', delay: '1.2s', x: '25%', y: '75%' },
    { Icon: Leaf, size: 'w-5 h-5', color: 'text-green-400', delay: '3.5s', x: '65%', y: '55%' },
    { Icon: Globe, size: 'w-4 h-4', color: 'text-emerald-500', delay: '0.3s', x: '45%', y: '40%' },
    { Icon: Cloud, size: 'w-3 h-3', color: 'text-blue-200', delay: '2.2s', x: '80%', y: '80%' },
    { Icon: Sun, size: 'w-4 h-4', color: 'text-yellow-300', delay: '1.7s', x: '10%', y: '70%' },
    { Icon: Droplets, size: 'w-4 h-4', color: 'text-cyan-300', delay: '3.8s', x: '55%', y: '20%' },
    { Icon: Wind, size: 'w-6 h-6', color: 'text-green-200', delay: '0.7s', x: '35%', y: '85%' },
    { Icon: TreePine, size: 'w-3 h-3', color: 'text-emerald-600', delay: '2.1s', x: '75%', y: '45%' },
  ];

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Main galaxy spiral background */}
      <div className="absolute inset-0">
        {/* Large spiral arms */}
        <div 
          className="absolute w-full h-full opacity-20"
          style={{
            background: `
              conic-gradient(from 0deg at 20% 30%, 
                transparent 0deg, 
                rgba(16, 185, 129, 0.1) 45deg, 
                transparent 90deg,
                rgba(5, 150, 105, 0.15) 180deg,
                transparent 225deg,
                rgba(16, 185, 129, 0.1) 315deg,
                transparent 360deg
              )
            `,
            animation: 'spin 120s linear infinite'
          }}
        />
        
        {/* Secondary spiral layer */}
        <div 
          className="absolute w-full h-full opacity-15"
          style={{
            background: `
              conic-gradient(from 120deg at 80% 70%, 
                transparent 0deg, 
                rgba(34, 197, 94, 0.1) 60deg, 
                transparent 120deg,
                rgba(22, 163, 74, 0.1) 240deg,
                transparent 300deg
              )
            `,
            animation: 'spin 180s linear infinite reverse'
          }}
        />
      </div>

      {/* Animated climate objects */}
      {climateObjects.map((obj, index) => {
        const { Icon, size, color, delay, x, y } = obj;
        return (
          <div
            key={index}
            className="absolute animate-float opacity-60 hover:opacity-100 transition-opacity duration-1000"
            style={{
              left: x,
              top: y,
              animationDelay: delay,
              animationDuration: `${4 + (index % 3)}s`
            }}
          >
            <div className={`${size} ${color} animate-pulse`}>
              <Icon className="w-full h-full drop-shadow-lg" />
            </div>
          </div>
        );
      })}

      {/* Floating energy particles */}
      {Array.from({ length: 15 }).map((_, index) => (
        <div
          key={`particle-${index}`}
          className="absolute w-1 h-1 bg-emerald-400 rounded-full animate-float opacity-40"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animationDelay: `${Math.random() * 5}s`,
            animationDuration: `${3 + Math.random() * 4}s`
          }}
        />
      ))}

      {/* Large floating orbs representing climate systems */}
      <div 
        className="absolute w-32 h-32 rounded-full opacity-10 animate-float"
        style={{
          background: 'radial-gradient(circle, rgba(16, 185, 129, 0.3) 0%, transparent 70%)',
          left: '15%',
          top: '25%',
          animationDelay: '1s',
          animationDuration: '8s'
        }}
      />
      
      <div 
        className="absolute w-24 h-24 rounded-full opacity-15 animate-float"
        style={{
          background: 'radial-gradient(circle, rgba(34, 197, 94, 0.4) 0%, transparent 70%)',
          right: '20%',
          top: '60%',
          animationDelay: '3s',
          animationDuration: '6s'
        }}
      />
      
      <div 
        className="absolute w-40 h-40 rounded-full opacity-8 animate-float"
        style={{
          background: 'radial-gradient(circle, rgba(5, 150, 105, 0.2) 0%, transparent 70%)',
          left: '60%',
          bottom: '20%',
          animationDelay: '0s',
          animationDuration: '10s'
        }}
      />

      {/* Ambient glow effects */}
      <div className="absolute inset-0 bg-gradient-radial from-emerald-500/5 via-transparent to-transparent opacity-50" />
      
      {/* Subtle animated overlay for depth */}
      <div 
        className="absolute inset-0 opacity-5"
        style={{
          background: `
            radial-gradient(ellipse at 30% 40%, rgba(16, 185, 129, 0.3) 0%, transparent 50%),
            radial-gradient(ellipse at 70% 80%, rgba(34, 197, 94, 0.2) 0%, transparent 60%),
            radial-gradient(ellipse at 90% 20%, rgba(5, 150, 105, 0.25) 0%, transparent 40%)
          `,
          animation: 'pulse 8s ease-in-out infinite'
        }}
      />
    </div>
  );
};

export default ClimateGalaxy;
