
import { <PERSON><PERSON><PERSON>, Shield, Users, Globe, Leaf, <PERSON>Pine, Sprout } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ClimateGalaxy from './ClimateGalaxy';

const Hero = () => {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-emerald-50 via-white to-green-50">
      {/* Climate Galaxy Background */}
      <ClimateGalaxy />
      
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-72 h-72 bg-emerald-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float"></div>
        <div className="absolute top-40 right-10 w-72 h-72 bg-green-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-emerald-50 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float" style={{ animationDelay: '4s' }}></div>
      </div>
      
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="text-left">
            <div className="mb-6">
              <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-emerald-100 text-emerald-700 mb-4">
                🌍 Building Climate Resilience
              </span>
            </div>
            
            <h1 className="text-5xl lg:text-7xl font-bold text-gray-900 mb-6 leading-tight font-inter">
              Empowering Communities to{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 to-green-600">
                Withstand Climate Disasters
              </span>
            </h1>
            
            <p className="text-xl text-gray-600 mb-8 max-w-2xl leading-relaxed font-inter font-light">
              The Climate Disasters International Foundation (CDIF) strengthens disaster resilience through inclusive, 
              community-driven programs that prepare, protect, and empower vulnerable communities.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 mb-12">
              <Button 
                size="lg" 
                className="bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white px-8 py-4 rounded-xl text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                onClick={() => scrollToSection('get-involved')}
              >
                Get Involved
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button 
                size="lg" 
                variant="outline" 
                className="border-2 border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300"
                onClick={() => scrollToSection('about')}
              >
                Learn More
              </Button>
            </div>

            {/* Modern Stats */}
            <div className="grid grid-cols-3 gap-8">
              <div className="text-center lg:text-left">
                <div className="flex items-center justify-center lg:justify-start mb-2">
                  <Shield className="h-6 w-6 text-emerald-600 mr-2" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900 mb-1 font-inter">25</h3>
                <p className="text-gray-600 text-sm font-medium">Board Members</p>
              </div>
              <div className="text-center lg:text-left">
                <div className="flex items-center justify-center lg:justify-start mb-2">
                  <Users className="h-6 w-6 text-emerald-600 mr-2" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900 mb-1 font-inter">4</h3>
                <p className="text-gray-600 text-sm font-medium">Core Programs</p>
              </div>
              <div className="text-center lg:text-left">
                <div className="flex items-center justify-center lg:justify-start mb-2">
                  <Globe className="h-6 w-6 text-emerald-600 mr-2" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900 mb-1 font-inter">2024</h3>
                <p className="text-gray-600 text-sm font-medium">Established</p>
              </div>
            </div>
          </div>

          {/* Right Content - Modern Animated Tree */}
          <div className="relative flex justify-center lg:justify-end">
            <div className="relative w-full max-w-lg">
              {/* Modern Tree Visualization */}
              <div className="relative bg-gradient-to-br from-emerald-50/80 to-green-50/80 backdrop-blur-md rounded-3xl p-12 shadow-2xl border border-white/20">
                
                {/* Animated Tree Structure */}
                <div className="relative flex flex-col items-center justify-center h-96">
                  
                  {/* Tree Crown - Multiple layers */}
                  <div className="relative">
                    {/* Top layer */}
                    <div className="w-32 h-32 bg-gradient-to-br from-emerald-400 to-green-500 rounded-full animate-float opacity-90 flex items-center justify-center">
                      <Leaf className="w-16 h-16 text-white animate-pulse" />
                    </div>
                    
                    {/* Middle layer */}
                    <div className="absolute -bottom-8 -left-8 w-40 h-40 bg-gradient-to-br from-emerald-300 to-green-400 rounded-full animate-float opacity-80 flex items-center justify-center" style={{ animationDelay: '1s' }}>
                      <TreePine className="w-20 h-20 text-white/90" />
                    </div>
                    
                    {/* Bottom layer */}
                    <div className="absolute -bottom-16 -right-8 w-36 h-36 bg-gradient-to-br from-emerald-500 to-green-600 rounded-full animate-float opacity-85 flex items-center justify-center" style={{ animationDelay: '2s' }}>
                      <Sprout className="w-18 h-18 text-white/95" />
                    </div>
                  </div>
                  
                  {/* Tree Trunk */}
                  <div className="w-6 bg-gradient-to-b from-amber-600 to-amber-800 h-24 rounded-full mt-4 animate-pulse"></div>
                  
                  {/* Tree Roots */}
                  <div className="flex space-x-4 mt-2">
                    <div className="w-16 h-2 bg-gradient-to-r from-amber-700 to-transparent rounded-full animate-float" style={{ animationDelay: '0.5s' }}></div>
                    <div className="w-20 h-2 bg-gradient-to-l from-amber-700 to-transparent rounded-full animate-float" style={{ animationDelay: '1.5s' }}></div>
                  </div>
                </div>
                
                {/* Floating Particles */}
                <div className="absolute top-8 right-8 w-3 h-3 bg-emerald-400 rounded-full animate-float"></div>
                <div className="absolute top-16 left-6 w-2 h-2 bg-green-400 rounded-full animate-float" style={{ animationDelay: '1s' }}></div>
                <div className="absolute bottom-20 right-12 w-4 h-4 bg-emerald-300 rounded-full animate-float" style={{ animationDelay: '2s' }}></div>
                <div className="absolute bottom-32 left-8 w-2 h-2 bg-green-500 rounded-full animate-float" style={{ animationDelay: '3s' }}></div>
                <div className="absolute top-1/2 right-4 w-3 h-3 bg-emerald-500 rounded-full animate-float" style={{ animationDelay: '0.8s' }}></div>
                
                {/* Animated Growth Rings */}
                <div className="absolute inset-0 rounded-3xl border-2 border-emerald-200 animate-ping opacity-20"></div>
                <div className="absolute inset-2 rounded-3xl border border-green-300 animate-ping opacity-30" style={{ animationDelay: '1s' }}></div>
              </div>
              
              {/* Glowing Background Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-400/10 to-green-400/10 rounded-3xl blur-2xl -z-10 animate-pulse"></div>
              
              {/* Modern Floating Elements */}
              <div className="absolute -top-6 -right-6 w-12 h-12 bg-gradient-to-br from-emerald-400 to-green-500 rounded-2xl rotate-45 animate-float shadow-lg"></div>
              <div className="absolute -bottom-6 -left-6 w-8 h-8 bg-gradient-to-br from-green-400 to-emerald-500 rounded-xl rotate-12 animate-float shadow-lg" style={{ animationDelay: '1.5s' }}></div>
              <div className="absolute top-1/3 -right-8 w-6 h-6 bg-gradient-to-br from-emerald-300 to-green-400 rounded-lg rotate-45 animate-float shadow-md" style={{ animationDelay: '2.5s' }}></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
