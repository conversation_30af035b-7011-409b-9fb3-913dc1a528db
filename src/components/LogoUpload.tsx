
import React from 'react';
import { useLogoManagement } from '@/hooks/useLogoManagement';
import LogoDisplay from '@/components/LogoDisplay';
import LogoUploadInput from '@/components/LogoUploadInput';

const LogoUpload = () => {
  const { uploading, currentLogo, uploadLogo, deleteLogo } = useLogoManagement();

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Organization Logo</h3>
        <p className="text-sm text-gray-600 mb-4">
          Upload a logo for your organization. Recommended size: 200x200 pixels. The logo will upload automatically when you select a file.
        </p>
      </div>

      <LogoDisplay 
        currentLogo={currentLogo}
        uploading={uploading}
        onDelete={deleteLogo}
      />

      <LogoUploadInput 
        uploading={uploading}
        onUpload={uploadLogo}
      />
    </div>
  );
};

export default LogoUpload;
