
import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import LogoUpload from '@/components/LogoUpload';

const Settings = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">Organization Settings</h1>
              <p className="text-gray-600">Manage your organization's settings and branding.</p>
            </div>
            
            <LogoUpload />
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default Settings;
