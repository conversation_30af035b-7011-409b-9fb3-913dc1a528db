
import { useState } from 'react';
import { Calendar, ExternalLink, Globe, MapPin, RefreshCw, Search } from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useClimateNews } from '@/hooks/useClimateNews';
import NewsLoadingSkeleton from '@/components/NewsLoadingSkeleton';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const ClimateNews = () => {
  const { data: newsItems, isLoading, error, refetch, isFetching } = useClimateNews();
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState<'all' | 'local' | 'global'>('all');

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const handleRefresh = () => {
    refetch();
  };

  const filteredNews = newsItems?.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filter === 'all' || 
                         (filter === 'local' && item.isLocal) || 
                         (filter === 'global' && !item.isLocal);
    return matchesSearch && matchesFilter;
  });

  if (error) {
    console.error('Error loading news:', error);
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-20">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-emerald-100 text-emerald-700 mb-6">
            📰 Climate News Hub
          </div>
          <div className="flex items-center justify-center gap-4 mb-6">
            <h1 className="text-4xl lg:text-5xl font-bold text-gray-900">
              Climate News &{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 to-green-600">
                Updates
              </span>
            </h1>
            <Button
              onClick={handleRefresh}
              variant="ghost"
              size="sm"
              className="text-emerald-600 hover:text-emerald-700 hover:bg-emerald-50"
              disabled={isFetching}
            >
              <RefreshCw className={`w-5 h-5 ${isFetching ? 'animate-spin' : ''}`} />
            </Button>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Stay informed about climate change impacts, resilience efforts, and environmental developments 
            in South Africa and around the world.
          </p>
          {isFetching && !isLoading && (
            <div className="inline-flex items-center px-3 py-1 rounded-full text-sm text-emerald-600 bg-emerald-50 mt-4">
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              Updating news...
            </div>
          )}
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col md:flex-row gap-4 mb-12">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search news articles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant={filter === 'all' ? 'default' : 'outline'}
              onClick={() => setFilter('all')}
              className={filter === 'all' ? 'bg-emerald-600 hover:bg-emerald-700' : ''}
            >
              All News
            </Button>
            <Button
              variant={filter === 'local' ? 'default' : 'outline'}
              onClick={() => setFilter('local')}
              className={filter === 'local' ? 'bg-emerald-600 hover:bg-emerald-700' : ''}
            >
              Local
            </Button>
            <Button
              variant={filter === 'global' ? 'default' : 'outline'}
              onClick={() => setFilter('global')}
              className={filter === 'global' ? 'bg-emerald-600 hover:bg-emerald-700' : ''}
            >
              Global
            </Button>
          </div>
        </div>

        {/* News Content */}
        {isLoading ? (
          <NewsLoadingSkeleton />
        ) : (
          <>
            {/* Featured Article */}
            {filteredNews && filteredNews.length > 0 && (
              <div className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Featured Story</h2>
                <Card className="overflow-hidden">
                  <div className="md:flex">
                    <div className="md:w-1/2">
                      <img
                        src={filteredNews[0].image}
                        alt={filteredNews[0].title}
                        className="w-full h-64 md:h-full object-cover"
                      />
                    </div>
                    <div className="md:w-1/2 p-8">
                      <div className="flex items-center gap-4 mb-4">
                        <Badge className={`${
                          filteredNews[0].isLocal 
                            ? 'bg-emerald-100 text-emerald-700' 
                            : 'bg-blue-100 text-blue-700'
                        }`}>
                          {filteredNews[0].isLocal ? <MapPin className="w-3 h-3 mr-1" /> : <Globe className="w-3 h-3 mr-1" />}
                          {filteredNews[0].category}
                        </Badge>
                        <span className="text-sm text-gray-500">
                          {formatDate(filteredNews[0].date)}
                        </span>
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-4">
                        {filteredNews[0].title}
                      </h3>
                      <p className="text-gray-600 mb-6 leading-relaxed">
                        {filteredNews[0].excerpt}
                      </p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-sm text-gray-500">
                          <MapPin className="w-4 h-4 mr-1" />
                          {filteredNews[0].location}
                        </div>
                        <Button className="bg-emerald-600 hover:bg-emerald-700">
                          Read More
                          <ExternalLink className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            )}

            {/* News Grid */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Latest News</h2>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredNews?.slice(1).map((item, index) => (
                  <Card 
                    key={item.id} 
                    className="group hover:shadow-2xl transition-all duration-500 border-0 bg-white/80 backdrop-blur-sm overflow-hidden"
                  >
                    <div className="relative overflow-hidden">
                      <img
                        src={item.image}
                        alt={item.title}
                        className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                      <div className="absolute top-4 left-4">
                        <Badge className={`${
                          item.isLocal 
                            ? 'bg-emerald-100 text-emerald-700' 
                            : 'bg-blue-100 text-blue-700'
                        }`}>
                          {item.isLocal ? <MapPin className="w-3 h-3 mr-1" /> : <Globe className="w-3 h-3 mr-1" />}
                          {item.category}
                        </Badge>
                      </div>
                    </div>

                    <CardHeader className="pb-3">
                      <div className="flex items-center text-sm text-gray-500 mb-2">
                        <Calendar className="w-4 h-4 mr-2" />
                        {formatDate(item.date)}
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 leading-tight group-hover:text-emerald-600 transition-colors">
                        {item.title}
                      </h3>
                    </CardHeader>

                    <CardContent className="pt-0">
                      <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                        {item.excerpt}
                      </p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-xs text-gray-500">
                          <MapPin className="w-3 h-3 mr-1" />
                          {item.location}
                        </div>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="text-emerald-600 hover:text-emerald-700 hover:bg-emerald-50 p-2"
                          onClick={() => item.url && window.open(item.url, '_blank')}
                        >
                          <ExternalLink className="w-4 h-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* No results message */}
            {filteredNews?.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">No news articles found matching your criteria.</p>
              </div>
            )}
          </>
        )}

        {/* Newsletter Signup */}
        <div className="bg-emerald-600 text-white rounded-lg p-8 text-center">
          <h3 className="text-2xl font-bold mb-4">Stay Updated</h3>
          <p className="text-lg mb-6 opacity-90">
            Subscribe to our newsletter for the latest climate news and updates.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <Input 
              placeholder="Enter your email" 
              className="bg-white text-gray-900"
            />
            <Button variant="outline" className="border-white text-white hover:bg-white hover:text-emerald-600">
              Subscribe
            </Button>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default ClimateNews;
