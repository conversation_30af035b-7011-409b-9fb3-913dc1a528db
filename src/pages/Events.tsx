
import { useState } from 'react';
import { Calendar, MapPin, Clock, Users, ExternalLink, Filter } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  type: 'workshop' | 'fundraiser' | 'conference' | 'training' | 'community';
  image: string;
  capacity: number;
  registered: number;
  price: number;
  featured: boolean;
}

const Events = () => {
  const [filter, setFilter] = useState<string>('all');

  const events: Event[] = [
    {
      id: '1',
      title: 'Climate Resilience Workshop: Building Stronger Communities',
      description: 'Learn practical strategies for disaster preparedness and community resilience building. Open to community leaders, volunteers, and concerned citizens.',
      date: '2024-07-15',
      time: '09:00 - 16:00',
      location: 'Cape Town Community Center',
      type: 'workshop',
      image: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      capacity: 50,
      registered: 23,
      price: 0,
      featured: true
    },
    {
      id: '2',
      title: 'Annual Fundraising Gala: Hope for Tomorrow',
      description: 'Join us for an evening of inspiration, networking, and fundraising to support our climate disaster resilience programs.',
      date: '2024-08-20',
      time: '18:00 - 23:00',
      location: 'The Waterfront Hotel, Cape Town',
      type: 'fundraiser',
      image: 'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      capacity: 200,
      registered: 87,
      price: 500,
      featured: true
    },
    {
      id: '3',
      title: 'Emergency Response Training for Volunteers',
      description: 'Comprehensive training program for new volunteers covering emergency response protocols, first aid, and disaster management.',
      date: '2024-07-08',
      time: '08:00 - 17:00',
      location: 'CDIF Training Center, Johannesburg',
      type: 'training',
      image: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      capacity: 30,
      registered: 18,
      price: 0,
      featured: false
    },
    {
      id: '4',
      title: 'Climate Change Conference: Southern Africa Perspectives',
      description: 'A two-day conference bringing together experts, policymakers, and community leaders to discuss climate adaptation strategies.',
      date: '2024-09-12',
      time: '08:30 - 17:30',
      location: 'University of Cape Town',
      type: 'conference',
      image: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      capacity: 300,
      registered: 145,
      price: 150,
      featured: false
    },
    {
      id: '5',
      title: 'Community Garden Project Launch',
      description: 'Help us launch a new community garden project focused on climate-resilient farming practices and food security.',
      date: '2024-07-25',
      time: '10:00 - 14:00',
      location: 'Khayelitsha Community Center',
      type: 'community',
      image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      capacity: 75,
      registered: 34,
      price: 0,
      featured: false
    }
  ];

  const eventTypes = [
    { value: 'all', label: 'All Events' },
    { value: 'workshop', label: 'Workshops' },
    { value: 'training', label: 'Training' },
    { value: 'fundraiser', label: 'Fundraisers' },
    { value: 'conference', label: 'Conferences' },
    { value: 'community', label: 'Community Events' }
  ];

  const filteredEvents = filter === 'all' 
    ? events 
    : events.filter(event => event.type === filter);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      weekday: 'long',
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const getTypeColor = (type: string) => {
    const colors = {
      workshop: 'bg-blue-100 text-blue-700',
      training: 'bg-green-100 text-green-700',
      fundraiser: 'bg-purple-100 text-purple-700',
      conference: 'bg-orange-100 text-orange-700',
      community: 'bg-emerald-100 text-emerald-700'
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-700';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-20">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Upcoming <span className="text-emerald-600">Events</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Join us for workshops, training sessions, fundraising events, and community gatherings 
            focused on building climate resilience.
          </p>
        </div>

        {/* Filter Section */}
        <div className="flex flex-wrap items-center gap-4 mb-12">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-500" />
            <span className="font-medium text-gray-700">Filter by type:</span>
          </div>
          {eventTypes.map((type) => (
            <Button
              key={type.value}
              variant={filter === type.value ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter(type.value)}
              className={filter === type.value ? 'bg-emerald-600 hover:bg-emerald-700' : ''}
            >
              {type.label}
            </Button>
          ))}
        </div>

        {/* Events Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {filteredEvents.map((event) => (
            <Card key={event.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="relative">
                <img 
                  src={event.image} 
                  alt={event.title}
                  className="w-full h-48 object-cover"
                />
                {event.featured && (
                  <Badge className="absolute top-4 left-4 bg-emerald-600">
                    Featured
                  </Badge>
                )}
                <Badge className={`absolute top-4 right-4 ${getTypeColor(event.type)}`}>
                  {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                </Badge>
              </div>
              
              <CardHeader>
                <CardTitle className="text-lg">{event.title}</CardTitle>
                <p className="text-sm text-gray-600 line-clamp-3">{event.description}</p>
              </CardHeader>
              
              <CardContent className="space-y-3">
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="h-4 w-4 mr-2" />
                  {formatDate(event.date)}
                </div>
                
                <div className="flex items-center text-sm text-gray-600">
                  <Clock className="h-4 w-4 mr-2" />
                  {event.time}
                </div>
                
                <div className="flex items-center text-sm text-gray-600">
                  <MapPin className="h-4 w-4 mr-2" />
                  {event.location}
                </div>
                
                <div className="flex items-center text-sm text-gray-600">
                  <Users className="h-4 w-4 mr-2" />
                  {event.registered}/{event.capacity} registered
                </div>
                
                <div className="flex items-center justify-between pt-4">
                  <div className="text-lg font-semibold text-emerald-600">
                    {event.price === 0 ? 'Free' : `R${event.price}`}
                  </div>
                  <Button className="bg-emerald-600 hover:bg-emerald-700">
                    Register
                    <ExternalLink className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Call to Action */}
        <div className="bg-emerald-600 text-white rounded-lg p-8 text-center">
          <h3 className="text-2xl font-bold mb-4">Want to Host an Event?</h3>
          <p className="text-lg mb-6 opacity-90">
            Partner with us to organize climate resilience events in your community.
          </p>
          <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-emerald-600">
            Contact Us About Events
          </Button>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Events;
