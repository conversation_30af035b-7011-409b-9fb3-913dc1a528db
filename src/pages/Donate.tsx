
import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Heart, Shield, Users, Zap, Check, CreditCard } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const Donate = () => {
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null);
  const [customAmount, setCustomAmount] = useState('');

  const predefinedAmounts = [25, 50, 100, 250, 500, 1000];

  const donationImpacts = [
    {
      amount: 25,
      impact: "Provides emergency supplies for 1 family during a disaster",
      icon: <Shield className="h-5 w-5" />
    },
    {
      amount: 50,
      impact: "Funds disaster preparedness training for 5 community members",
      icon: <Users className="h-5 w-5" />
    },
    {
      amount: 100,
      impact: "Supports early warning systems for an entire village",
      icon: <Zap className="h-5 w-5" />
    },
    {
      amount: 250,
      impact: "Helps build climate-resilient infrastructure in vulnerable areas",
      icon: <Heart className="h-5 w-5" />
    }
  ];

  const features = [
    "100% of donations go directly to programs",
    "Tax-deductible donations",
    "Regular impact reports",
    "Transparent fund allocation"
  ];

  const handleDonate = () => {
    const amount = selectedAmount || parseFloat(customAmount);
    if (amount && amount > 0) {
      // In a real implementation, this would integrate with a payment processor
      console.log(`Initiating donation of R${amount}`);
      alert(`Thank you! You would be redirected to payment processor for R${amount} donation.`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-20">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <Badge className="bg-emerald-100 text-emerald-700 mb-4">
            💝 Make a Difference Today
          </Badge>
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Support Climate <span className="text-emerald-600">Resilience</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Your donation helps vulnerable communities prepare for, respond to, and recover from climate disasters.
            Every contribution makes a real difference.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-12 max-w-7xl mx-auto">
          {/* Donation Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl text-gray-900">Choose Your Donation Amount</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Predefined Amounts */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-3 block">
                    Select Amount (ZAR)
                  </label>
                  <div className="grid grid-cols-3 gap-3">
                    {predefinedAmounts.map((amount) => (
                      <Button
                        key={amount}
                        variant={selectedAmount === amount ? "default" : "outline"}
                        className={`h-12 ${selectedAmount === amount ? 'bg-emerald-600 hover:bg-emerald-700' : ''}`}
                        onClick={() => {
                          setSelectedAmount(amount);
                          setCustomAmount('');
                        }}
                      >
                        R{amount}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Custom Amount */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    Or Enter Custom Amount
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">R</span>
                    <input
                      type="number"
                      placeholder="0"
                      className="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      value={customAmount}
                      onChange={(e) => {
                        setCustomAmount(e.target.value);
                        setSelectedAmount(null);
                      }}
                      min="1"
                    />
                  </div>
                </div>

                {/* Impact Display */}
                {(selectedAmount || parseFloat(customAmount) > 0) && (
                  <div className="bg-emerald-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-emerald-800 mb-2">Your Impact</h4>
                    <p className="text-emerald-700 text-sm">
                      Your donation of R{selectedAmount || parseFloat(customAmount)} will help us continue our vital work 
                      in building climate resilience and supporting vulnerable communities.
                    </p>
                  </div>
                )}

                {/* Donation Button */}
                <Button 
                  onClick={handleDonate}
                  className="w-full bg-emerald-600 hover:bg-emerald-700 h-12 text-lg"
                  disabled={!selectedAmount && !parseFloat(customAmount)}
                >
                  <CreditCard className="mr-2 h-5 w-5" />
                  Donate Now
                </Button>

                {/* Security Notice */}
                <p className="text-xs text-gray-500 text-center">
                  🔒 Secure donation processing. Your information is protected and never shared.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Impact Examples */}
            <Card>
              <CardHeader>
                <CardTitle>Your Donation Impact</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {donationImpacts.map((item, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div className="text-emerald-600 mt-1">
                      {item.icon}
                    </div>
                    <div>
                      <div className="font-semibold text-emerald-600">R{item.amount}</div>
                      <p className="text-sm text-gray-600">{item.impact}</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Features */}
            <Card>
              <CardHeader>
                <CardTitle>Why Donate With Us?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <Check className="h-4 w-4 text-emerald-600" />
                    <span className="text-sm text-gray-700">{feature}</span>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Contact for Large Donations */}
            <Card>
              <CardHeader>
                <CardTitle>Large Donations</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-3">
                  For donations over R10,000 or to discuss partnership opportunities, 
                  please contact us directly.
                </p>
                <Button variant="outline" className="w-full">
                  Contact Us
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Donate;
