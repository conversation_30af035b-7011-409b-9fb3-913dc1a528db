
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';

interface NewsItem {
  id: string;
  title: string;
  excerpt: string;
  image: string;
  location: string;
  date: string;
  category: string;
  isLocal: boolean;
  url?: string;
}

// Fallback news data with real event images
const fallbackNews: NewsItem[] = [
  {
    id: '1',
    title: "South Africa Faces Severe Drought Crisis as Cape Town Water Levels Drop",
    excerpt: "The Western Cape experiences its worst drought in decades, with dam levels falling below critical thresholds and communities implementing water restrictions.",
    image: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    location: "Cape Town, South Africa",
    date: new Date().toISOString().split('T')[0],
    category: "Local",
    isLocal: true
  },
  {
    id: '2',
    title: "KwaZulu-Natal Flooding Displaces Thousands in Informal Settlements",
    excerpt: "Heavy rains and flash floods devastate communities across KwaZulu-Natal, highlighting the vulnerability of informal settlements to climate disasters.",
    image: "https://images.unsplash.com/photo-1547036967-23d11aacaee0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    location: "KwaZulu-Natal, South Africa",
    date: new Date(Date.now() - 86400000 * 3).toISOString().split('T')[0],
    category: "Local",
    isLocal: true
  },
  {
    id: '3',
    title: "Global Climate Report Shows Record-Breaking Temperatures Worldwide",
    excerpt: "New research reveals 2024 on track to be the hottest year on record, with significant implications for vulnerable communities globally.",
    image: "https://images.unsplash.com/photo-1569163139394-de4e5f43e4e3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    location: "Global",
    date: new Date(Date.now() - 86400000 * 5).toISOString().split('T')[0],
    category: "Global",
    isLocal: false
  }
];

const fetchClimateNews = async (): Promise<NewsItem[]> => {
  try {
    // You can replace this with a real news API like NewsAPI, Guardian API, etc.
    // For now, we'll simulate fetching updated news with some variations
    console.log('Fetching latest climate news...');
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Generate updated news with current timestamps and real event images
    const updatedNews = fallbackNews.map((item, index) => ({
      ...item,
      date: new Date(Date.now() - 86400000 * index).toISOString().split('T')[0],
      id: `${item.id}-${Date.now()}-${index}`
    }));

    // Add some dynamic content based on current time with real climate action images
    const now = new Date();
    const timeBasedNews: NewsItem[] = [
      {
        id: `dynamic-${now.getTime()}`,
        title: `Climate Action Update: ${now.toLocaleDateString()}`,
        excerpt: "Latest developments in South Africa's climate resilience efforts and community preparedness initiatives across the region.",
        image: "https://images.unsplash.com/photo-1473773508845-188df298d2d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        location: "South Africa",
        date: now.toISOString().split('T')[0],
        category: "Local",
        isLocal: true
      }
    ];

    return [...timeBasedNews, ...updatedNews].slice(0, 6);
  } catch (error) {
    console.error('Error fetching climate news:', error);
    return fallbackNews;
  }
};

export const useClimateNews = () => {
  return useQuery({
    queryKey: ['climate-news'],
    queryFn: fetchClimateNews,
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    staleTime: 2 * 60 * 1000, // Consider data stale after 2 minutes
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });
};
