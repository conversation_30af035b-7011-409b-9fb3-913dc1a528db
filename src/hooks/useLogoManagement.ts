
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export const useLogoManagement = () => {
  const [uploading, setUploading] = useState(false);
  const [currentLogo, setCurrentLogo] = useState<string | null>(null);
  const [logoFilename, setLogoFilename] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    fetchCurrentLogo();
  }, []);

  const fetchCurrentLogo = async () => {
    try {
      const { data, error } = await supabase
        .from('organization_settings')
        .select('logo_url, logo_filename')
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching logo:', error);
        return;
      }

      if (data?.logo_url) {
        setCurrentLogo(data.logo_url);
        setLogoFilename(data.logo_filename);
      }
    } catch (error) {
      console.error('Error fetching current logo:', error);
    }
  };

  const uploadLogo = async (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      setUploading(true);

      if (!event.target.files || event.target.files.length === 0) {
        throw new Error('You must select an image to upload.');
      }

      const file = event.target.files[0];
      const fileExt = file.name.split('.').pop();
      const fileName = `logo-${Date.now()}.${fileExt}`;
      const filePath = fileName;

      // Upload file to storage
      const { error: uploadError } = await supabase.storage
        .from('org-logos')
        .upload(filePath, file);

      if (uploadError) {
        throw uploadError;
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('org-logos')
        .getPublicUrl(filePath);

      const logoUrl = urlData.publicUrl;

      // First, get the existing record ID or create a new one
      const { data: existingData } = await supabase
        .from('organization_settings')
        .select('id')
        .limit(1)
        .single();

      let updateResult;
      if (existingData?.id) {
        // Update existing record
        updateResult = await supabase
          .from('organization_settings')
          .update({
            logo_url: logoUrl,
            logo_filename: fileName,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingData.id);
      } else {
        // Insert new record
        updateResult = await supabase
          .from('organization_settings')
          .insert({
            logo_url: logoUrl,
            logo_filename: fileName
          });
      }

      if (updateResult.error) {
        throw updateResult.error;
      }

      setCurrentLogo(logoUrl);
      setLogoFilename(fileName);
      
      toast({
        title: "Success",
        description: "Logo uploaded successfully!",
      });

      // Refresh the page to update the header logo
      window.location.reload();

    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Error uploading logo',
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const deleteLogo = async () => {
    try {
      setUploading(true);

      // Delete from storage if filename exists
      if (logoFilename) {
        const { error: deleteError } = await supabase.storage
          .from('org-logos')
          .remove([logoFilename]);

        if (deleteError) {
          console.error('Error deleting file from storage:', deleteError);
        }
      }

      // Get the existing record ID
      const { data: existingData } = await supabase
        .from('organization_settings')
        .select('id')
        .limit(1)
        .single();

      if (existingData?.id) {
        // Update organization settings to remove logo
        const { error: updateError } = await supabase
          .from('organization_settings')
          .update({
            logo_url: null,
            logo_filename: null,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingData.id);

        if (updateError) {
          throw updateError;
        }
      }

      setCurrentLogo(null);
      setLogoFilename(null);
      
      toast({
        title: "Success",
        description: "Logo deleted successfully!",
      });

      // Refresh the page to update the header logo
      window.location.reload();

    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Error deleting logo',
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  return {
    uploading,
    currentLogo,
    logoFilename,
    uploadLogo,
    deleteLogo
  };
};
