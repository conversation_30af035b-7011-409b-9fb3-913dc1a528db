
/* Modern design approach using Tailwind CSS - minimal custom styles needed */

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Custom gradient animations */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-animation {
  background-size: 200% 200%;
  animation: gradient-shift 6s ease infinite;
}

/* Enhanced focus states for accessibility */
.focus-enhanced:focus {
  outline: 2px solid #10b981;
  outline-offset: 2px;
  border-radius: 0.75rem;
}
