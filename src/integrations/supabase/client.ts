// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://kjqarglhlznkxvbxqbao.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtqcWFyZ2xobHpua3h2YnhxYmFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyMjc3MTEsImV4cCI6MjA2NTgwMzcxMX0.m46cq_v4LyfkmoXMgIZctXJiCY3pPLRpMa3cHQb1hTY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);